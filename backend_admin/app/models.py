# -*- coding: utf-8 -*-
"""数据库模型"""
import json
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from . import db

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True, nullable=False)
    email = db.Column(db.String(120), unique=True, index=True, nullable=False)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.<PERSON>, default=False)
    is_paid_member = db.Column(db.<PERSON>, default=False)
    membership_expiry = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)

    def is_membership_valid(self):
        """检查会员是否有效"""
        if not self.is_paid_member:
            return False
        if not self.membership_expiry:
            return False
        return self.membership_expiry > datetime.now()

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "is_admin": self.is_admin,
            "is_paid_member": self.is_paid_member,
            "membership_valid": self.is_membership_valid(),
            "membership_expiry": self.membership_expiry.strftime("%Y-%m-%d %H:%M:%S") if self.membership_expiry else None,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

class MembershipPlan(db.Model):
    """会员计划模型"""
    __tablename__ = 'membership_plans'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    duration_months = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    is_active = db.Column(db.Boolean, default=True)

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "price": self.price,
            "duration_months": self.duration_months,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

class Order(db.Model):
    """订单模型"""
    __tablename__ = 'orders'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    plan_id = db.Column(db.Integer, db.ForeignKey('membership_plans.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed
    created_at = db.Column(db.DateTime, default=datetime.now)
    paid_at = db.Column(db.DateTime)

    # 关系
    user = db.relationship('User', backref=db.backref('orders', lazy='dynamic'))
    plan = db.relationship('MembershipPlan', backref=db.backref('orders', lazy='dynamic'))

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "plan_id": self.plan_id,
            "amount": self.amount,
            "status": self.status,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "paid_at": self.paid_at.strftime("%Y-%m-%d %H:%M:%S") if self.paid_at else None,
            "plan": self.plan.to_dict() if self.plan else None,
            "user": {"id": self.user.id, "username": self.user.username} if self.user else None
        }

class Software(db.Model):
    """软件模型"""
    __tablename__ = 'software'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.now)
    is_active = db.Column(db.Boolean, default=True)

    # 关系
    versions = db.relationship('SoftwareVersion', backref='software', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """转换为字典"""
        latest_version = self.versions.order_by(SoftwareVersion.version_number.desc()).first()
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "latest_version": latest_version.to_dict() if latest_version else None
        }

class SoftwareVersion(db.Model):
    """软件版本模型"""
    __tablename__ = 'software_versions'

    id = db.Column(db.Integer, primary_key=True)
    software_id = db.Column(db.Integer, db.ForeignKey('software.id'), nullable=False)
    version_number = db.Column(db.String(20), nullable=False)
    release_notes = db.Column(db.Text)
    file_path = db.Column(db.String(255), nullable=True)  # 允许为空，用于本地上传的文件
    external_url = db.Column(db.String(255), nullable=True)  # 新增：外部URL
    is_external = db.Column(db.Boolean, default=False)  # 新增：是否使用外部URL
    created_at = db.Column(db.DateTime, default=datetime.now)
    is_active = db.Column(db.Boolean, default=True)

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "software_id": self.software_id,
            "version_number": self.version_number,
            "release_notes": self.release_notes,
            "is_external": self.is_external,
            "external_url": self.external_url if self.is_external else None,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "is_active": self.is_active
        }

class MidjourneyTask(db.Model):
    """Midjourney任务模型"""
    __tablename__ = 'midjourney_tasks'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    task_id = db.Column(db.String(64), nullable=False)  # Midjourney API任务ID
    prompt = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed
    result_url = db.Column(db.String(255))  # 结果图片URL
    error_message = db.Column(db.Text)  # 错误信息
    raw_response = db.Column(db.Text)  # 原始API响应
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关系
    user = db.relationship('User', backref=db.backref('midjourney_tasks', lazy='dynamic'))

    def to_dict(self):
        """转换为字典"""
        data = {
            "id": self.id,
            "user_id": self.user_id,
            "task_id": self.task_id,
            "prompt": self.prompt,
            "status": self.status,
            "result_url": self.result_url,
            "error_message": self.error_message,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        # 如果有原始响应，解析并添加到结果中
        if self.raw_response:
            try:
                raw_data = json.loads(self.raw_response)
                data["api_response"] = raw_data
            except:
                pass  # 如果无法解析JSON，忽略

        return data
