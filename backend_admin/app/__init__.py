# -*- coding: utf-8 -*-
"""应用初始化"""
import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from config import config

db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()

def create_app(config_name=None):
    """创建应用实例"""
    if config_name is None:
        config_name = os.getenv("FLASK_CONFIG") or "default"

    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 修复重定向问题 - 使用ProxyFix处理反向代理
    from werkzeug.middleware.proxy_fix import ProxyFix
    app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)

    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    CORS(app)

    # 注册蓝图
    from .routes.auth import auth_bp
    from .routes.midjourney import midjourney_bp
    from .routes.software import software_bp
    from .routes.admin import admin_bp
    from .routes.main import main_bp
    from .routes.admin_dashboard import admin_dashboard_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp, url_prefix="/api/auth")
    app.register_blueprint(midjourney_bp, url_prefix="/api/midjourney")
    app.register_blueprint(software_bp, url_prefix="/api/software")
    app.register_blueprint(admin_bp, url_prefix="/api/admin")
    app.register_blueprint(admin_dashboard_bp, url_prefix="/admin")

    # 创建必要的目录
    with app.app_context():
        # 创建数据目录
        data_dir = os.path.join(app.root_path, '..', 'data')
        os.makedirs(data_dir, exist_ok=True)

        # 创建上传目录
        uploads_dir = os.path.join(app.root_path, "uploads")
        software_dir = os.path.join(uploads_dir, "software")
        os.makedirs(uploads_dir, exist_ok=True)
        os.makedirs(software_dir, exist_ok=True)

        # 创建静态文件目录
        static_dir = os.path.join(app.root_path, "static")
        admin_static_dir = os.path.join(static_dir, "admin")
        os.makedirs(static_dir, exist_ok=True)
        os.makedirs(admin_static_dir, exist_ok=True)

    return app
