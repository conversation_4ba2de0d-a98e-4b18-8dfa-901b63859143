# -*- coding: utf-8 -*-
"""主页路由"""
from flask import Blueprint, jsonify, render_template_string

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """主页"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>XHS Backend Admin API</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                line-height: 1.6;
                background-color: #f8f9fa;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                border-bottom: 2px solid #007bff;
                padding-bottom: 10px;
                margin-bottom: 20px;
            }
            h2 {
                color: #444;
                margin-top: 30px;
                border-left: 4px solid #007bff;
                padding-left: 15px;
            }
            code {
                background-color: #f5f5f5;
                padding: 2px 5px;
                border-radius: 3px;
                font-family: monospace;
                color: #e83e8c;
            }
            pre {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                overflow-x: auto;
                border-left: 4px solid #007bff;
            }
            .endpoint {
                margin-bottom: 15px;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 5px;
            }
            .method {
                font-weight: bold;
                color: #fff;
                padding: 2px 8px;
                border-radius: 3px;
                font-size: 12px;
            }
            .method.get { background-color: #28a745; }
            .method.post { background-color: #007bff; }
            .method.put { background-color: #ffc107; color: #000; }
            .method.delete { background-color: #dc3545; }
            .admin-link {
                display: inline-block;
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 5px;
                margin-top: 20px;
            }
            .admin-link:hover {
                background-color: #0056b3;
                color: white;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 XHS Backend Admin API</h1>
            <p><strong>欢迎使用 XHS 后端管理系统 API</strong></p>
            <p>这是一个用于管理小红书自动发布和AI图片生成平台的后端管理系统。</p>
            <p>Welcome to the XHS Backend Admin API. This is a management system for Xiaohongshu auto-publishing and AI image generation platform.</p>
            
            <a href="/admin" class="admin-link">🔧 进入管理后台 / Admin Dashboard</a>
            
            <h2>📋 API 端点 (API Endpoints)</h2>
            
            <h3>🔐 认证 (Authentication)</h3>
            <div class="endpoint">
                <p><span class="method post">POST</span> <code>/api/auth/register</code> - 注册新用户</p>
                <p><span class="method post">POST</span> <code>/api/auth/login</code> - 用户登录</p>
                <p><span class="method post">POST</span> <code>/api/auth/refresh</code> - 刷新访问令牌</p>
                <p><span class="method get">GET</span> <code>/api/auth/profile</code> - 获取用户资料</p>
                <p><span class="method put">PUT</span> <code>/api/auth/profile</code> - 更新用户资料</p>
            </div>
            
            <h3>💾 软件管理 (Software)</h3>
            <div class="endpoint">
                <p><span class="method get">GET</span> <code>/api/software/list</code> - 获取软件列表</p>
                <p><span class="method get">GET</span> <code>/api/software/{software_id}</code> - 获取软件详情</p>
                <p><span class="method get">GET</span> <code>/api/software/download/{version_id}</code> - 下载软件</p>
                <p><span class="method get">GET</span> <code>/api/software/check-update/{name}/{version}</code> - 检查更新</p>
            </div>
            
            <h3>🎨 Midjourney AI</h3>
            <div class="endpoint">
                <p><span class="method post">POST</span> <code>/api/midjourney/generate</code> - 生成AI图像</p>
                <p><span class="method get">GET</span> <code>/api/midjourney/tasks/{task_id}</code> - 获取任务状态</p>
                <p><span class="method get">GET</span> <code>/api/midjourney/tasks</code> - 获取用户任务列表</p>
            </div>
            
            <h3>👑 管理员 (Admin)</h3>
            <div class="endpoint">
                <p><span class="method get">GET</span> <code>/admin/stats</code> - 获取系统统计</p>
                <p><span class="method get">GET</span> <code>/admin/users</code> - 用户管理</p>
                <p><span class="method get">GET</span> <code>/admin/software</code> - 软件管理</p>
                <p><span class="method get">GET</span> <code>/admin/membership-plans</code> - 会员计划管理</p>
            </div>
            
            <h2>🔧 使用方法 (Usage)</h2>
            <p>所有 API 请求都需要 JSON 格式的请求体，并返回 JSON 格式的响应。</p>
            <p>除了注册和登录端点外，所有请求都需要在请求头中包含 JWT 令牌：</p>
            
            <pre>Authorization: Bearer {your_access_token}</pre>
            
            <h2>📝 示例 (Example)</h2>
            <p><strong>登录请求示例：</strong></p>
            
            <pre>
POST /api/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "admin123"
}
            </pre>
            
            <p><strong>响应示例：</strong></p>
            
            <pre>
{
    "success": true,
    "message": "登录成功",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "is_admin": true,
        "is_paid_member": true,
        "membership_valid": true
    }
}
            </pre>
            
            <h2>🌟 功能特点</h2>
            <ul>
                <li>🔐 JWT 认证系统</li>
                <li>👥 用户和会员管理</li>
                <li>💾 软件版本管理和分发</li>
                <li>🎨 AI 图像生成集成</li>
                <li>📊 完整的管理后台</li>
                <li>🔒 权限控制系统</li>
            </ul>
        </div>
    </body>
    </html>
    """
    return render_template_string(html)
