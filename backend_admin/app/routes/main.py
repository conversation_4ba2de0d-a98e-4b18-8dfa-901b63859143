# -*- coding: utf-8 -*-
"""主页路由"""
from flask import Blueprint, jsonify, render_template_string

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """主页"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>XHS Backend Admin API</title>
        <style>
            :root {
                --primary-color: #10b981;
                --primary-dark: #059669;
                --primary-light: #34d399;
                --secondary-color: #f0fdf4;
                --accent-color: #065f46;
                --text-dark: #1f2937;
                --text-light: #6b7280;
                --bg-light: #f9fafb;
                --border-color: #e5e7eb;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                max-width: 900px;
                margin: 0 auto;
                padding: 2rem;
                line-height: 1.7;
                background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
                color: var(--text-dark);
                min-height: 100vh;
            }

            .container {
                background: rgba(255, 255, 255, 0.95);
                padding: 3rem;
                border-radius: 20px;
                box-shadow: 0 20px 60px rgba(16, 185, 129, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(16, 185, 129, 0.1);
            }

            h1 {
                color: var(--text-dark);
                border-bottom: 3px solid var(--primary-color);
                padding-bottom: 1rem;
                margin-bottom: 2rem;
                font-weight: 700;
                font-size: 2.5rem;
                background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            h2 {
                color: var(--text-dark);
                margin-top: 2.5rem;
                margin-bottom: 1.5rem;
                border-left: 4px solid var(--primary-color);
                padding-left: 1rem;
                font-weight: 600;
                font-size: 1.5rem;
            }

            h3 {
                color: var(--accent-color);
                margin-top: 2rem;
                margin-bottom: 1rem;
                font-weight: 600;
                font-size: 1.2rem;
            }

            code {
                background: linear-gradient(135deg, var(--secondary-color), #f0fdf4);
                padding: 0.25rem 0.5rem;
                border-radius: 6px;
                font-family: 'JetBrains Mono', 'Fira Code', monospace;
                color: var(--accent-color);
                font-weight: 500;
                border: 1px solid rgba(16, 185, 129, 0.2);
            }

            pre {
                background: linear-gradient(135deg, var(--secondary-color), #f0fdf4);
                padding: 1.5rem;
                border-radius: 12px;
                overflow-x: auto;
                border-left: 4px solid var(--primary-color);
                font-family: 'JetBrains Mono', 'Fira Code', monospace;
                color: var(--accent-color);
                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
            }

            .endpoint {
                margin-bottom: 1rem;
                padding: 1.5rem;
                background: linear-gradient(135deg, rgba(240, 253, 244, 0.5), rgba(236, 253, 245, 0.5));
                border-radius: 12px;
                border: 1px solid rgba(16, 185, 129, 0.1);
                transition: all 0.3s ease;
            }

            .endpoint:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
            }

            .method {
                font-weight: 600;
                color: #fff;
                padding: 0.25rem 0.75rem;
                border-radius: 6px;
                font-size: 0.75rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-right: 0.5rem;
            }

            .method.get { background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); }
            .method.post { background: linear-gradient(135deg, #3b82f6, #60a5fa); }
            .method.put { background: linear-gradient(135deg, #f59e0b, #fbbf24); color: #000; }
            .method.delete { background: linear-gradient(135deg, #ef4444, #f87171); }

            .admin-link {
                display: inline-block;
                background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
                color: white;
                padding: 1rem 2rem;
                text-decoration: none;
                border-radius: 12px;
                margin: 2rem 0;
                font-weight: 600;
                font-size: 1.1rem;
                transition: all 0.3s ease;
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            }

            .admin-link:hover {
                background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
                color: white;
                text-decoration: none;
                transform: translateY(-3px);
                box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
            }

            ul {
                list-style: none;
                padding-left: 0;
            }

            ul li {
                padding: 0.5rem 0;
                padding-left: 2rem;
                position: relative;
            }

            ul li::before {
                content: '✨';
                position: absolute;
                left: 0;
                color: var(--primary-color);
            }

            p {
                margin-bottom: 1rem;
                color: var(--text-dark);
            }

            strong {
                color: var(--accent-color);
                font-weight: 600;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 XHS Backend Admin API</h1>
            <p><strong>欢迎使用 XHS 后端管理系统 API</strong></p>
            <p>这是一个用于管理小红书自动发布和AI图片生成平台的后端管理系统。</p>
            <p>Welcome to the XHS Backend Admin API. This is a management system for Xiaohongshu auto-publishing and AI image generation platform.</p>

            <a href="/admin" class="admin-link">🔧 进入管理后台 / Admin Dashboard</a>

            <h2>📋 API 端点 (API Endpoints)</h2>

            <h3>🔐 认证 (Authentication)</h3>
            <div class="endpoint">
                <p><span class="method post">POST</span> <code>/api/auth/register</code> - 注册新用户</p>
                <p><span class="method post">POST</span> <code>/api/auth/login</code> - 用户登录</p>
                <p><span class="method post">POST</span> <code>/api/auth/refresh</code> - 刷新访问令牌</p>
                <p><span class="method get">GET</span> <code>/api/auth/profile</code> - 获取用户资料</p>
                <p><span class="method put">PUT</span> <code>/api/auth/profile</code> - 更新用户资料</p>
            </div>

            <h3>💾 软件管理 (Software)</h3>
            <div class="endpoint">
                <p><span class="method get">GET</span> <code>/api/software/list</code> - 获取软件列表</p>
                <p><span class="method get">GET</span> <code>/api/software/{software_id}</code> - 获取软件详情</p>
                <p><span class="method get">GET</span> <code>/api/software/download/{version_id}</code> - 下载软件</p>
                <p><span class="method get">GET</span> <code>/api/software/check-update/{name}/{version}</code> - 检查更新</p>
            </div>

            <h3>🎨 Midjourney AI</h3>
            <div class="endpoint">
                <p><span class="method post">POST</span> <code>/api/midjourney/generate</code> - 生成AI图像</p>
                <p><span class="method get">GET</span> <code>/api/midjourney/tasks/{task_id}</code> - 获取任务状态</p>
                <p><span class="method get">GET</span> <code>/api/midjourney/tasks</code> - 获取用户任务列表</p>
            </div>

            <h3>👑 管理员 (Admin)</h3>
            <div class="endpoint">
                <p><span class="method get">GET</span> <code>/admin/stats</code> - 获取系统统计</p>
                <p><span class="method get">GET</span> <code>/admin/users</code> - 用户管理</p>
                <p><span class="method get">GET</span> <code>/admin/software</code> - 软件管理</p>
                <p><span class="method get">GET</span> <code>/admin/membership-plans</code> - 会员计划管理</p>
            </div>

            <h2>🔧 使用方法 (Usage)</h2>
            <p>所有 API 请求都需要 JSON 格式的请求体，并返回 JSON 格式的响应。</p>
            <p>除了注册和登录端点外，所有请求都需要在请求头中包含 JWT 令牌：</p>

            <pre>Authorization: Bearer {your_access_token}</pre>

            <h2>📝 示例 (Example)</h2>
            <p><strong>登录请求示例：</strong></p>

            <pre>
POST /api/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "admin123"
}
            </pre>

            <p><strong>响应示例：</strong></p>

            <pre>
{
    "success": true,
    "message": "登录成功",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "is_admin": true,
        "is_paid_member": true,
        "membership_valid": true
    }
}
            </pre>

            <h2>🌟 功能特点</h2>
            <ul>
                <li>🔐 JWT 认证系统</li>
                <li>👥 用户和会员管理</li>
                <li>💾 软件版本管理和分发</li>
                <li>🎨 AI 图像生成集成</li>
                <li>📊 完整的管理后台</li>
                <li>🔒 权限控制系统</li>
            </ul>
        </div>
    </body>
    </html>
    """
    return render_template_string(html)
