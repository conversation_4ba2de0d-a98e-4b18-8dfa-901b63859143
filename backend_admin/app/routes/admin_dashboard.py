# -*- coding: utf-8 -*-
"""管理员后台路由"""
import os
import json
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, render_template_string, current_app, redirect, url_for, send_from_directory
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from .. import db
from ..models import User, Software, SoftwareVersion, MembershipPlan, MidjourneyTask
from ..utils.decorators import admin_required

admin_dashboard_bp = Blueprint('admin_dashboard', __name__)

@admin_dashboard_bp.route('/')
@admin_dashboard_bp.route('')
def index():
    """管理后台首页"""
    html = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>XHS 后端管理系统</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; }
            .login-container { max-width: 400px; margin: 100px auto; }
            .card { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
            .btn-primary { background-color: #007bff; border-color: #007bff; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="login-container">
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="bi bi-shield-lock"></i> 管理员登录</h4>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" class="form-control" id="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">登录</button>
                        </form>
                        <div id="message" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const messageDiv = document.getElementById('message');

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username, password })
                    });

                    const data = await response.json();

                    if (data.success) {
                        if (data.user.is_admin) {
                            localStorage.setItem('access_token', data.access_token);
                            localStorage.setItem('user', JSON.stringify(data.user));
                            // 使用相对路径避免域名问题
                            window.location.href = './dashboard';
                        } else {
                            messageDiv.innerHTML = '<div class="alert alert-danger">需要管理员权限</div>';
                        }
                    } else {
                        messageDiv.innerHTML = '<div class="alert alert-danger">' + data.message + '</div>';
                    }
                } catch (error) {
                    messageDiv.innerHTML = '<div class="alert alert-danger">登录失败，请重试</div>';
                }
            });
        </script>
    </body>
    </html>
    """
    return render_template_string(html)

@admin_dashboard_bp.route('/dashboard')
def dashboard():
    """管理后台控制面板"""
    html = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>XHS 后端管理系统 - 控制面板</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; }
            .sidebar { min-height: 100vh; background-color: #343a40; }
            .sidebar .nav-link { color: #adb5bd; }
            .sidebar .nav-link:hover, .sidebar .nav-link.active { color: #fff; background-color: #495057; }
            .main-content { padding: 20px; }
            .stat-card { transition: transform 0.2s; }
            .stat-card:hover { transform: translateY(-5px); }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <!-- 侧边栏 -->
                <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <div class="text-center mb-4">
                            <h5 class="text-white">XHS 管理系统</h5>
                        </div>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link active" href="#dashboard" onclick="showSection('dashboard')">
                                    <i class="bi bi-speedometer2"></i> 控制面板
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#users" onclick="showSection('users')">
                                    <i class="bi bi-people"></i> 用户管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#software" onclick="showSection('software')">
                                    <i class="bi bi-box"></i> 软件管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#membership" onclick="showSection('membership')">
                                    <i class="bi bi-star"></i> 会员管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#settings" onclick="showSection('settings')">
                                    <i class="bi bi-gear"></i> 系统设置
                                </a>
                            </li>
                            <li class="nav-item mt-3">
                                <a class="nav-link text-danger" href="#" onclick="logout()">
                                    <i class="bi bi-box-arrow-right"></i> 退出登录
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- 主内容区 -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                    <!-- 控制面板 -->
                    <div id="dashboard-section" class="section">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">控制面板</h1>
                        </div>

                        <div class="row" id="stats-cards">
                            <!-- 统计卡片将通过JavaScript动态加载 -->
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>系统状态</h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-success"><i class="bi bi-check-circle"></i> 系统运行正常</p>
                                        <p><strong>服务器时间:</strong> <span id="server-time"></span></p>
                                        <p><strong>API状态:</strong> <span class="text-success">正常</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户管理 -->
                    <div id="users-section" class="section" style="display: none;">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">用户管理</h1>
                            <button class="btn btn-primary" onclick="showCreateUserModal()">
                                <i class="bi bi-plus"></i> 创建用户
                            </button>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="users-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>用户名</th>
                                                <th>邮箱</th>
                                                <th>管理员</th>
                                                <th>会员状态</th>
                                                <th>注册时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 用户数据将通过JavaScript动态加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 软件管理 -->
                    <div id="software-section" class="section" style="display: none;">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">软件管理</h1>
                            <button class="btn btn-primary" onclick="showCreateSoftwareModal()">
                                <i class="bi bi-plus"></i> 添加软件
                            </button>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div id="software-list">
                                    <!-- 软件列表将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 会员管理 -->
                    <div id="membership-section" class="section" style="display: none;">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">会员管理</h1>
                            <button class="btn btn-primary" onclick="showCreatePlanModal()">
                                <i class="bi bi-plus"></i> 创建会员计划
                            </button>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div id="membership-plans">
                                    <!-- 会员计划将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统设置 -->
                    <div id="settings-section" class="section" style="display: none;">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">系统设置</h1>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5>API 配置</h5>
                            </div>
                            <div class="card-body">
                                <form id="settings-form">
                                    <div class="mb-3">
                                        <label for="midjourney_api_key" class="form-label">Midjourney API Key</label>
                                        <input type="text" class="form-control" id="midjourney_api_key">
                                    </div>
                                    <div class="mb-3">
                                        <label for="midjourney_api_base" class="form-label">Midjourney API Base URL</label>
                                        <input type="text" class="form-control" id="midjourney_api_base">
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // 检查登录状态
            function checkAuth() {
                const token = localStorage.getItem('access_token');
                const user = JSON.parse(localStorage.getItem('user') || '{}');

                if (!token || !user.is_admin) {
                    window.location.href = '../';
                    return false;
                }
                return true;
            }

            // API请求封装
            async function apiRequest(url, options = {}) {
                const token = localStorage.getItem('access_token');
                const defaultOptions = {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                };

                const response = await fetch(url, { ...defaultOptions, ...options });

                if (response.status === 401) {
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('user');
                    window.location.href = '../';
                    return null;
                }

                return response.json();
            }

            // 显示指定部分
            function showSection(sectionName) {
                // 隐藏所有部分
                document.querySelectorAll('.section').forEach(section => {
                    section.style.display = 'none';
                });

                // 显示指定部分
                document.getElementById(sectionName + '-section').style.display = 'block';

                // 更新导航状态
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                document.querySelector(`[href="#${sectionName}"]`).classList.add('active');

                // 加载对应数据
                switch(sectionName) {
                    case 'dashboard':
                        loadStats();
                        break;
                    case 'users':
                        loadUsers();
                        break;
                    case 'software':
                        loadSoftware();
                        break;
                    case 'membership':
                        loadMembershipPlans();
                        break;
                    case 'settings':
                        loadSettings();
                        break;
                }
            }

            // 加载统计数据
            async function loadStats() {
                try {
                    const data = await apiRequest('/admin/stats');
                    if (data && data.success) {
                        const stats = data.stats;
                        document.getElementById('stats-cards').innerHTML = `
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>${stats.total_users}</h4>
                                                <p>总用户数</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="bi bi-people fs-1"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>${stats.active_paid_users}</h4>
                                                <p>活跃会员</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="bi bi-star fs-1"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>${stats.total_software || 0}</h4>
                                                <p>软件数量</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="bi bi-box fs-1"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>${stats.total_tasks}</h4>
                                                <p>AI任务数</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="bi bi-cpu fs-1"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            // 加载用户列表
            async function loadUsers() {
                try {
                    const data = await apiRequest('/admin/users');
                    if (data && data.success) {
                        const tbody = document.querySelector('#users-table tbody');
                        tbody.innerHTML = data.users.map(user => `
                            <tr>
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${user.email}</td>
                                <td>${user.is_admin ? '<span class="badge bg-danger">是</span>' : '<span class="badge bg-secondary">否</span>'}</td>
                                <td>${user.membership_valid ? '<span class="badge bg-success">有效</span>' : '<span class="badge bg-secondary">无效</span>'}</td>
                                <td>${user.created_at}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.id})">编辑</button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})">删除</button>
                                </td>
                            </tr>
                        `).join('');
                    }
                } catch (error) {
                    console.error('加载用户列表失败:', error);
                }
            }

            // 加载软件列表
            async function loadSoftware() {
                try {
                    const data = await apiRequest('/admin/software');
                    if (data && data.success) {
                        const container = document.getElementById('software-list');
                        container.innerHTML = data.software.map(software => `
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h5 class="card-title">${software.name}</h5>
                                            <p class="card-text">${software.description || '暂无描述'}</p>
                                            <small class="text-muted">创建时间: ${software.created_at}</small>
                                        </div>
                                        <div>
                                            <span class="badge ${software.is_active ? 'bg-success' : 'bg-secondary'}">${software.is_active ? '启用' : '禁用'}</span>
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="manageSoftwareVersions(${software.id})">版本管理</button>
                                            <button class="btn btn-sm btn-outline-secondary ms-1" onclick="editSoftware(${software.id})">编辑</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                    }
                } catch (error) {
                    console.error('加载软件列表失败:', error);
                }
            }

            // 加载会员计划
            async function loadMembershipPlans() {
                try {
                    const data = await apiRequest('/admin/membership-plans');
                    if (data && data.success) {
                        const container = document.getElementById('membership-plans');
                        container.innerHTML = data.plans.map(plan => `
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h5 class="card-title">${plan.name}</h5>
                                            <p class="card-text">${plan.description || '暂无描述'}</p>
                                            <p><strong>价格:</strong> ¥${plan.price} | <strong>时长:</strong> ${plan.duration_months}个月</p>
                                        </div>
                                        <div>
                                            <span class="badge ${plan.is_active ? 'bg-success' : 'bg-secondary'}">${plan.is_active ? '启用' : '禁用'}</span>
                                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="editPlan(${plan.id})">编辑</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                    }
                } catch (error) {
                    console.error('加载会员计划失败:', error);
                }
            }

            // 加载设置
            async function loadSettings() {
                try {
                    const data = await apiRequest('/admin/settings');
                    if (data && data.success) {
                        document.getElementById('midjourney_api_key').value = data.settings.midjourney_api_key || '';
                        document.getElementById('midjourney_api_base').value = data.settings.midjourney_api_base || '';
                    }
                } catch (error) {
                    console.error('加载设置失败:', error);
                }
            }

            // 退出登录
            function logout() {
                localStorage.removeItem('access_token');
                localStorage.removeItem('user');
                window.location.href = '/admin/';
            }

            // 更新服务器时间
            function updateServerTime() {
                document.getElementById('server-time').textContent = new Date().toLocaleString('zh-CN');
            }

            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                if (checkAuth()) {
                    loadStats();
                    updateServerTime();
                    setInterval(updateServerTime, 1000);
                }
            });

            // 占位函数，实际功能需要进一步实现
            function showCreateUserModal() { alert('创建用户功能待实现'); }
            function showCreateSoftwareModal() { alert('创建软件功能待实现'); }
            function showCreatePlanModal() { alert('创建会员计划功能待实现'); }
            function editUser(id) { alert('编辑用户功能待实现: ' + id); }
            function deleteUser(id) { alert('删除用户功能待实现: ' + id); }
            function manageSoftwareVersions(id) { alert('版本管理功能待实现: ' + id); }
            function editSoftware(id) { alert('编辑软件功能待实现: ' + id); }
            function editPlan(id) { alert('编辑会员计划功能待实现: ' + id); }
        </script>
    </body>
    </html>
    """
    return render_template_string(html)

# API端点 - 获取统计数据
@admin_dashboard_bp.route('/stats')
@jwt_required()
@admin_required
def get_stats():
    """获取统计数据"""
    total_users = User.query.count()
    paid_users = User.query.filter_by(is_paid_member=True).count()
    active_paid_users = 0

    for user in User.query.filter_by(is_paid_member=True).all():
        if user.is_membership_valid():
            active_paid_users += 1

    total_software = Software.query.count()
    total_tasks = MidjourneyTask.query.count()

    return jsonify({
        "success": True,
        "stats": {
            "total_users": total_users,
            "paid_users": paid_users,
            "active_paid_users": active_paid_users,
            "total_software": total_software,
            "total_tasks": total_tasks
        }
    })

# API端点 - 软件管理
@admin_dashboard_bp.route('/software')
@jwt_required()
@admin_required
def list_software():
    """获取软件列表"""
    software_list = Software.query.all()
    return jsonify({
        "success": True,
        "software": [s.to_dict() for s in software_list]
    })

@admin_dashboard_bp.route('/software', methods=['POST'])
@jwt_required()
@admin_required
def create_software():
    """创建软件"""
    data = request.get_json()

    if not data or 'name' not in data:
        return jsonify({"success": False, "message": "缺少必要参数"}), 400

    # 检查软件名称是否已存在
    if Software.query.filter_by(name=data['name']).first():
        return jsonify({"success": False, "message": "软件名称已存在"}), 400

    software = Software(
        name=data['name'],
        description=data.get('description', ''),
        is_active=True,
        created_at=datetime.now()
    )

    db.session.add(software)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "软件创建成功",
        "software": software.to_dict()
    })

# API端点 - 用户管理
@admin_dashboard_bp.route('/users')
@jwt_required()
@admin_required
def list_users():
    """获取用户列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    pagination = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    users = pagination.items

    return jsonify({
        "success": True,
        "users": [user.to_dict() for user in users],
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": pagination.total,
            "pages": pagination.pages
        }
    })

# API端点 - 会员计划管理
@admin_dashboard_bp.route('/membership-plans')
@jwt_required()
@admin_required
def list_plans():
    """获取会员计划列表"""
    plans = MembershipPlan.query.all()

    return jsonify({
        "success": True,
        "plans": [plan.to_dict() for plan in plans]
    })

# API端点 - API设置
@admin_dashboard_bp.route('/settings')
@jwt_required()
@admin_required
def get_settings():
    """获取API设置"""
    # 从配置文件或数据库中获取设置
    settings = {
        "midjourney_api_key": current_app.config.get('MIDJOURNEY_API_KEY', ''),
        "midjourney_api_base": current_app.config.get('MIDJOURNEY_API_BASE', '')
    }

    return jsonify({
        "success": True,
        "settings": settings
    })

@admin_dashboard_bp.route('/settings', methods=['POST'])
@jwt_required()
@admin_required
def update_settings():
    """更新API设置"""
    data = request.get_json()

    if not data:
        return jsonify({"success": False, "message": "缺少必要参数"}), 400

    # 更新配置
    if 'midjourney_api_key' in data:
        current_app.config['MIDJOURNEY_API_KEY'] = data['midjourney_api_key']

    if 'midjourney_api_base' in data:
        current_app.config['MIDJOURNEY_API_BASE'] = data['midjourney_api_base']

    return jsonify({
        "success": True,
        "message": "设置已更新"
    })