# -*- coding: utf-8 -*-
"""管理员相关路由"""
import os
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from .. import db
from ..models import User, Software, SoftwareVersion, MembershipPlan, MidjourneyTask
from ..utils.decorators import admin_required

admin_bp = Blueprint("admin", __name__)

# 用户管理
@admin_bp.route("/users", methods=["GET"])
@jwt_required()
@admin_required
def get_users():
    """获取用户列表"""
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)
    search = request.args.get("search", "")

    # 构建查询
    query = User.query
    if search:
        query = query.filter(
            (User.username.ilike(f"%{search}%")) |
            (User.email.ilike(f"%{search}%"))
        )

    # 分页
    pagination = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    users = pagination.items

    return jsonify({
        "success": True,
        "users": [user.to_dict() for user in users],
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": pagination.total,
            "pages": pagination.pages
        }
    }), 200

@admin_bp.route("/users/<int:user_id>", methods=["GET"])
@jwt_required()
@admin_required
def get_user(user_id):
    """获取用户详情"""
    user = User.query.get(user_id)

    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404

    return jsonify({
        "success": True,
        "user": user.to_dict()
    }), 200

@admin_bp.route("/users/<int:user_id>", methods=["PUT"])
@jwt_required()
@admin_required
def update_user(user_id):
    """更新用户信息"""
    user = User.query.get(user_id)

    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404

    data = request.get_json()

    # 更新用户信息
    if 'email' in data:
        # 检查邮箱是否已存在
        existing_user = User.query.filter_by(email=data['email']).first()
        if existing_user and existing_user.id != user_id:
            return jsonify({"success": False, "message": "邮箱已存在"}), 400
        user.email = data['email']

    if 'is_admin' in data:
        user.is_admin = data['is_admin']

    if 'is_paid_member' in data:
        user.is_paid_member = data['is_paid_member']

    if 'membership_expiry' in data and data['membership_expiry']:
        try:
            user.membership_expiry = datetime.strptime(data['membership_expiry'], '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"success": False, "message": "日期格式错误"}), 400

    if 'password' in data and data['password']:
        user.set_password(data['password'])

    db.session.commit()

    return jsonify({
        "success": True,
        "message": "用户信息已更新",
        "user": user.to_dict()
    }), 200

@admin_bp.route("/users", methods=["POST"])
@jwt_required()
@admin_required
def create_user():
    """创建新用户"""
    data = request.get_json()

    # 验证必填字段
    if not all(k in data for k in ["username", "email", "password"]):
        return jsonify({"success": False, "message": "缺少必填字段"}), 400

    # 检查用户名和邮箱是否已存在
    if User.query.filter_by(username=data["username"]).first():
        return jsonify({"success": False, "message": "用户名已存在"}), 400

    if User.query.filter_by(email=data["email"]).first():
        return jsonify({"success": False, "message": "邮箱已存在"}), 400

    # 创建新用户
    user = User(username=data["username"], email=data["email"])
    user.set_password(data["password"])

    if 'is_admin' in data:
        user.is_admin = data['is_admin']

    if 'is_paid_member' in data:
        user.is_paid_member = data['is_paid_member']

    if 'membership_expiry' in data and data['membership_expiry']:
        try:
            user.membership_expiry = datetime.strptime(data['membership_expiry'], '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"success": False, "message": "日期格式错误"}), 400

    db.session.add(user)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "用户创建成功",
        "user": user.to_dict()
    }), 201

@admin_bp.route("/users/<int:user_id>", methods=["DELETE"])
@jwt_required()
@admin_required
def delete_user(user_id):
    """删除用户"""
    user = User.query.get(user_id)

    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404

    # 防止删除管理员自己
    current_user_id = get_jwt_identity()
    if user_id == current_user_id:
        return jsonify({"success": False, "message": "不能删除自己"}), 400

    db.session.delete(user)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "用户已删除"
    }), 200

# 软件管理
@admin_bp.route("/software", methods=["GET"])
@jwt_required()
@admin_required
def get_software_list():
    """获取软件列表"""
    software_list = Software.query.order_by(Software.created_at.desc()).all()

    return jsonify({
        "success": True,
        "software": [s.to_dict() for s in software_list]
    }), 200

@admin_bp.route("/software", methods=["POST"])
@jwt_required()
@admin_required
def create_software():
    """创建新软件"""
    data = request.get_json()

    # 验证必填字段
    if not data.get("name"):
        return jsonify({"success": False, "message": "软件名称不能为空"}), 400

    # 创建软件
    software = Software(
        name=data["name"],
        description=data.get("description", "")
    )

    db.session.add(software)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "软件创建成功",
        "software": software.to_dict()
    }), 201

@admin_bp.route("/software/<int:software_id>", methods=["PUT"])
@jwt_required()
@admin_required
def update_software(software_id):
    """更新软件信息"""
    software = Software.query.get(software_id)

    if not software:
        return jsonify({"success": False, "message": "软件不存在"}), 404

    data = request.get_json()

    if 'name' in data:
        software.name = data['name']
    if 'description' in data:
        software.description = data['description']
    if 'is_active' in data:
        software.is_active = data['is_active']

    db.session.commit()

    return jsonify({
        "success": True,
        "message": "软件信息已更新",
        "software": software.to_dict()
    }), 200

@admin_bp.route("/software/<int:software_id>", methods=["DELETE"])
@jwt_required()
@admin_required
def delete_software(software_id):
    """删除软件"""
    software = Software.query.get(software_id)

    if not software:
        return jsonify({"success": False, "message": "软件不存在"}), 404

    db.session.delete(software)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "软件已删除"
    }), 200

# 统计信息
@admin_bp.route("/stats", methods=["GET"])
@jwt_required()
@admin_required
def get_stats():
    """获取统计数据"""
    total_users = User.query.count()
    paid_users = User.query.filter_by(is_paid_member=True).count()

    # 计算活跃付费用户（会员未过期）
    active_paid_users = User.query.filter(
        User.is_paid_member == True,
        User.membership_expiry > datetime.now()
    ).count()

    total_software = Software.query.count()
    total_tasks = MidjourneyTask.query.count()

    # 最近7天的任务数
    week_ago = datetime.now() - timedelta(days=7)
    recent_tasks = MidjourneyTask.query.filter(
        MidjourneyTask.created_at >= week_ago
    ).count()

    return jsonify({
        "success": True,
        "stats": {
            "total_users": total_users,
            "paid_users": paid_users,
            "active_paid_users": active_paid_users,
            "total_software": total_software,
            "total_tasks": total_tasks,
            "recent_tasks": recent_tasks
        }
    }), 200

# 会员计划管理
@admin_bp.route("/membership-plans", methods=["GET"])
@jwt_required()
@admin_required
def get_membership_plans():
    """获取会员计划列表"""
    plans = MembershipPlan.query.order_by(MembershipPlan.created_at.desc()).all()

    return jsonify({
        "success": True,
        "plans": [plan.to_dict() for plan in plans]
    }), 200

@admin_bp.route("/membership-plans", methods=["POST"])
@jwt_required()
@admin_required
def create_membership_plan():
    """创建会员计划"""
    data = request.get_json()

    # 验证必填字段
    if not all(k in data for k in ["name", "price", "duration_months"]):
        return jsonify({"success": False, "message": "缺少必填字段"}), 400

    # 检查计划名称是否已存在
    if MembershipPlan.query.filter_by(name=data["name"]).first():
        return jsonify({"success": False, "message": "计划名称已存在"}), 400

    # 创建会员计划
    plan = MembershipPlan(
        name=data["name"],
        description=data.get("description", ""),
        price=float(data["price"]),
        duration_months=int(data["duration_months"])
    )

    db.session.add(plan)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "会员计划创建成功",
        "plan": plan.to_dict()
    }), 201

@admin_bp.route("/membership-plans/<int:plan_id>", methods=["PUT"])
@jwt_required()
@admin_required
def update_membership_plan(plan_id):
    """更新会员计划"""
    plan = MembershipPlan.query.get(plan_id)

    if not plan:
        return jsonify({"success": False, "message": "会员计划不存在"}), 404

    data = request.get_json()

    if 'name' in data:
        # 检查名称是否已被其他计划使用
        existing_plan = MembershipPlan.query.filter_by(name=data['name']).first()
        if existing_plan and existing_plan.id != plan_id:
            return jsonify({"success": False, "message": "计划名称已存在"}), 400
        plan.name = data['name']

    if 'description' in data:
        plan.description = data['description']
    if 'price' in data:
        plan.price = float(data['price'])
    if 'duration_months' in data:
        plan.duration_months = int(data['duration_months'])
    if 'is_active' in data:
        plan.is_active = data['is_active']

    db.session.commit()

    return jsonify({
        "success": True,
        "message": "会员计划已更新",
        "plan": plan.to_dict()
    }), 200

@admin_bp.route("/membership-plans/<int:plan_id>", methods=["DELETE"])
@jwt_required()
@admin_required
def delete_membership_plan(plan_id):
    """删除会员计划"""
    plan = MembershipPlan.query.get(plan_id)

    if not plan:
        return jsonify({"success": False, "message": "会员计划不存在"}), 404

    db.session.delete(plan)
    db.session.commit()

    return jsonify({
        "success": True,
        "message": "会员计划已删除"
    }), 200
