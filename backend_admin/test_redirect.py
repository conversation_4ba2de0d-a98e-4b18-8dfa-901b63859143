#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重定向修复是否生效
"""
import requests
import sys

def test_redirect():
    """测试重定向功能"""
    print("🔍 测试重定向修复效果")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "IP访问 - 不带斜杠",
            "url": "http://**************:5000/admin",
            "expected_redirect": "http://**************:5000/admin/"
        },
        {
            "name": "IP访问 - 带斜杠",
            "url": "http://**************:5000/admin/",
            "expected_redirect": None  # 不应该重定向
        },
        {
            "name": "域名访问 - 不带斜杠",
            "url": "http://ai.mohcdn.com:5000/admin",
            "expected_redirect": "http://ai.mohcdn.com:5000/admin/"
        },
        {
            "name": "域名访问 - 带斜杠",
            "url": "http://ai.mohcdn.com:5000/admin/",
            "expected_redirect": None  # 不应该重定向
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n📋 测试: {test_case['name']}")
        print(f"URL: {test_case['url']}")
        
        try:
            # 不自动跟随重定向，检查重定向响应
            response = requests.get(test_case['url'], allow_redirects=False, timeout=10)
            
            if response.status_code in [301, 302, 308]:
                # 有重定向
                location = response.headers.get('Location', '')
                print(f"状态码: {response.status_code}")
                print(f"重定向到: {location}")
                
                if test_case['expected_redirect']:
                    if location == test_case['expected_redirect']:
                        print("✅ 重定向正确")
                        results.append(True)
                    else:
                        print(f"❌ 重定向错误，期望: {test_case['expected_redirect']}")
                        results.append(False)
                else:
                    print("❌ 不应该有重定向")
                    results.append(False)
                    
            elif response.status_code == 200:
                # 直接返回内容
                print(f"状态码: {response.status_code}")
                print("✅ 直接访问成功")
                
                if test_case['expected_redirect']:
                    print("❌ 应该有重定向但没有")
                    results.append(False)
                else:
                    print("✅ 正确，无重定向")
                    results.append(True)
            else:
                print(f"❌ 意外状态码: {response.status_code}")
                results.append(False)
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_case, result) in enumerate(zip(test_cases, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {test_case['name']}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重定向修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False

def test_admin_login():
    """测试管理后台登录功能"""
    print("\n🔐 测试管理后台登录功能")
    print("=" * 60)
    
    # 测试登录API
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('user', {}).get('is_admin'):
                print("✅ 管理员登录成功")
                print(f"用户: {data['user']['username']}")
                print(f"邮箱: {data['user']['email']}")
                return True
            else:
                print("❌ 登录失败或非管理员用户")
                return False
        else:
            print(f"❌ 登录请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 XHS 后端管理系统 - 重定向测试")
    print("测试时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 测试重定向
    redirect_ok = test_redirect()
    
    # 测试登录
    login_ok = test_admin_login()
    
    print("\n" + "=" * 60)
    print("🏁 最终结果:")
    print(f"重定向功能: {'✅ 正常' if redirect_ok else '❌ 异常'}")
    print(f"登录功能: {'✅ 正常' if login_ok else '❌ 异常'}")
    
    if redirect_ok and login_ok:
        print("\n🎉 所有功能测试通过！系统运行正常！")
        print("\n📋 访问地址:")
        print("  主页: http://ai.mohcdn.com:5000")
        print("  管理后台: http://ai.mohcdn.com:5000/admin")
        print("  管理后台: http://ai.mohcdn.com:5000/admin/")
        print("  默认账号: admin / admin123")
        sys.exit(0)
    else:
        print("\n⚠️ 部分功能存在问题，请检查配置")
        sys.exit(1)
