#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""部署脚本"""
import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
    except subprocess.CalledProcessError:
        print("❌ 依赖包安装失败")
        sys.exit(1)

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")
    
    # 创建必要的目录
    directories = [
        "data",
        "logs",
        "app/uploads",
        "app/uploads/software",
        "app/static",
        "app/static/admin"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 创建目录: {directory}")
    
    print("✅ 环境设置完成")

def initialize_database():
    """初始化数据库"""
    print("🗄️ 初始化数据库...")
    try:
        subprocess.check_call([sys.executable, "create_admin.py"])
        print("✅ 数据库初始化完成")
    except subprocess.CalledProcessError:
        print("❌ 数据库初始化失败")
        sys.exit(1)

def create_systemd_service():
    """创建systemd服务文件 (仅Linux)"""
    if platform.system() != "Linux":
        return
    
    service_content = f"""[Unit]
Description=XHS Backend Admin
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory={os.getcwd()}
Environment=PATH={os.getcwd()}/venv/bin
ExecStart={sys.executable} run.py
Restart=always

[Install]
WantedBy=multi-user.target
"""
    
    service_file = "/etc/systemd/system/xhs-backend.service"
    try:
        with open(service_file, 'w') as f:
            f.write(service_content)
        print(f"✅ 创建systemd服务文件: {service_file}")
        print("💡 使用以下命令管理服务:")
        print("   sudo systemctl enable xhs-backend")
        print("   sudo systemctl start xhs-backend")
        print("   sudo systemctl status xhs-backend")
    except PermissionError:
        print("⚠️ 无权限创建systemd服务文件，请手动创建或使用sudo运行")

def create_nginx_config():
    """创建Nginx配置示例"""
    nginx_config = """# XHS Backend Admin Nginx配置示例
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件
    location /static {
        alias /path/to/backend_admin/app/static;  # 替换为实际路径
        expires 30d;
    }

    # 上传文件
    location /uploads {
        alias /path/to/backend_admin/app/uploads;  # 替换为实际路径
        expires 30d;
    }
}
"""
    
    with open("nginx.conf.example", 'w') as f:
        f.write(nginx_config)
    print("✅ 创建Nginx配置示例: nginx.conf.example")

def main():
    """主函数"""
    print("🚀 XHS 后端管理系统部署脚本")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 安装依赖
    install_dependencies()
    
    # 设置环境
    setup_environment()
    
    # 初始化数据库
    initialize_database()
    
    # 创建服务文件
    create_systemd_service()
    
    # 创建Nginx配置示例
    create_nginx_config()
    
    print("=" * 50)
    print("🎉 部署完成!")
    print("=" * 50)
    print("下一步:")
    print("1. 启动服务: python run.py")
    print("2. 访问管理后台: http://localhost:5000/admin")
    print("3. 默认管理员账号: admin / admin123")
    print("=" * 50)
    print("生产环境部署:")
    print("1. 配置Nginx (参考 nginx.conf.example)")
    print("2. 配置SSL证书")
    print("3. 使用Gunicorn或uWSGI运行")
    print("4. 配置防火墙")
    print("=" * 50)

if __name__ == "__main__":
    main()
