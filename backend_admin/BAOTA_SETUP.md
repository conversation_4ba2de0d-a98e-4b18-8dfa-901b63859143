# 🚀 宝塔面板部署XHS后端管理系统指南

## 📋 前置条件

- ✅ 宝塔面板已安装
- ✅ Python 3.8+ 已安装
- ✅ 项目文件已上传到服务器

## 🔧 宝塔面板配置步骤

### 第一步：安装Python环境

1. **安装Python管理器**
   - 在宝塔面板左侧菜单点击 "软件商店"
   - 搜索 "Python项目管理器"
   - 点击安装

2. **确认Python版本**
   - 确保服务器已安装Python 3.8或更高版本
   - 如未安装，在软件商店搜索安装Python

### 第二步：创建Python项目

1. **进入Python项目管理**
   - 点击左侧菜单 "网站"
   - 点击 "Python项目"

2. **添加新项目**
   - 点击 "添加Python项目"
   - 填写以下信息：

   ```
   项目名称: XHS后端管理系统
   域名: 你的域名或IP地址 (例如: api.yourdomain.com)
   项目路径: /www/wwwroot/mohcdn/backend_admin
   Python版本: Python 3.8
   框架: Flask
   启动文件: start_baota.py
   端口: 5000
   ```

3. **高级设置**
   - 启动方式: Gunicorn
   - 进程数: 2-4 (根据服务器配置)
   - 是否开机启动: 是

### 第三步：安装依赖包

1. **进入项目目录**
   ```bash
   cd /www/wwwroot/mohcdn/backend_admin
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

   或在宝塔面板的Python项目管理中：
   - 点击项目名称进入详情
   - 点击 "模块" 标签
   - 点击 "安装模块"
   - 选择 "从requirements.txt安装"

### 第四步：配置项目

1. **设置环境变量**
   在宝塔Python项目设置中添加环境变量：
   ```
   FLASK_CONFIG=baota
   SECRET_KEY=your-secret-key-here
   JWT_SECRET_KEY=your-jwt-secret-key-here
   ```

2. **设置项目权限**
   ```bash
   chmod -R 755 /www/wwwroot/mohcdn/backend_admin
   chown -R www:www /www/wwwroot/mohcdn/backend_admin
   ```

### 第五步：启动项目

1. **在宝塔面板中启动**
   - 在Python项目列表中找到你的项目
   - 点击 "启动" 按钮

2. **检查运行状态**
   - 状态应显示为 "运行中"
   - 查看日志确认无错误

### 第六步：配置反向代理 (可选)

如果需要通过80端口访问，配置Nginx反向代理：

1. **创建网站**
   - 点击 "网站" → "添加站点"
   - 域名: 你的域名
   - 根目录: 任意 (不会使用)

2. **配置反向代理**
   - 进入网站设置
   - 点击 "反向代理"
   - 添加反向代理：
     ```
     代理名称: XHS后端API
     目标URL: http://127.0.0.1:5000
     发送域名: $host
     ```

3. **Nginx配置示例**
   ```nginx
   location / {
       proxy_pass http://127.0.0.1:5000;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
   }
   
   location /static {
       alias /www/wwwroot/mohcdn/backend_admin/app/static;
       expires 30d;
   }
   
   location /uploads {
       alias /www/wwwroot/mohcdn/backend_admin/app/uploads;
       expires 30d;
   }
   ```

## 🌐 访问测试

### 直接访问 (端口5000)
```
http://你的IP:5000
http://你的IP:5000/admin
```

### 通过域名访问 (配置反向代理后)
```
http://你的域名.com
http://你的域名.com/admin
```

### 默认登录信息
- 用户名: `admin`
- 密码: `admin123`

## 🔧 常见问题解决

### 1. 项目启动失败
**检查步骤:**
```bash
# 查看项目日志
tail -f /www/wwwroot/mohcdn/backend_admin/logs/backend_admin.log

# 检查Python进程
ps aux | grep python

# 检查端口占用
netstat -tlnp | grep :5000
```

### 2. 依赖包安装失败
**解决方案:**
```bash
# 升级pip
pip install --upgrade pip

# 手动安装依赖
cd /www/wwwroot/mohcdn/backend_admin
pip install -r requirements.txt

# 如果还有问题，逐个安装
pip install Flask==2.2.3
pip install Flask-SQLAlchemy==3.0.3
# ... 其他依赖
```

### 3. 数据库权限问题
**解决方案:**
```bash
# 设置正确权限
chmod 755 /www/wwwroot/mohcdn/backend_admin/data
chmod 644 /www/wwwroot/mohcdn/backend_admin/data/*.sqlite
chown -R www:www /www/wwwroot/mohcdn/backend_admin/data
```

### 4. 静态文件无法访问
**解决方案:**
```bash
# 设置静态文件权限
chmod -R 755 /www/wwwroot/mohcdn/backend_admin/app/static
chmod -R 755 /www/wwwroot/mohcdn/backend_admin/app/uploads
```

## 🔒 安全配置

### 1. 防火墙设置
在宝塔面板安全设置中：
- 开放80端口 (HTTP)
- 开放443端口 (HTTPS)
- 如需直接访问，开放5000端口
- 关闭不必要的端口

### 2. SSL证书配置
1. 在网站设置中点击 "SSL"
2. 选择 "Let's Encrypt" 免费证书
3. 点击申请并部署

### 3. 修改默认密码
首次部署后立即：
1. 访问管理后台
2. 使用默认账号登录
3. 修改管理员密码

## 📊 监控和维护

### 1. 查看运行状态
```bash
# 查看Python进程
ps aux | grep start_baota.py

# 查看端口监听
netstat -tlnp | grep :5000

# 查看日志
tail -f /www/wwwroot/mohcdn/backend_admin/logs/backend_admin.log
```

### 2. 重启服务
在宝塔Python项目管理中：
- 点击 "重启" 按钮
- 或使用命令行: `systemctl restart bt_python_项目名`

### 3. 备份数据
```bash
# 备份数据库
cp /www/wwwroot/mohcdn/backend_admin/data/data.sqlite \
   /www/backup/xhs_backend_$(date +%Y%m%d_%H%M%S).sqlite

# 备份整个项目
tar -czf /www/backup/xhs_backend_$(date +%Y%m%d_%H%M%S).tar.gz \
    /www/wwwroot/mohcdn/backend_admin
```

## 🎯 完成检查清单

- [ ] Python环境已安装
- [ ] 项目已创建并配置
- [ ] 依赖包已安装
- [ ] 项目已启动运行
- [ ] 可以访问主页
- [ ] 可以访问管理后台
- [ ] 管理员账号可以登录
- [ ] API接口正常工作
- [ ] 反向代理已配置 (如需要)
- [ ] SSL证书已配置 (如需要)
- [ ] 防火墙已配置
- [ ] 默认密码已修改

---

**🎉 恭喜！XHS后端管理系统已在宝塔面板中成功部署！**

如遇问题，请检查宝塔面板的Python项目日志和系统日志。
