#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宝塔面板专用配置文件
"""
import os

# 项目根目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# 宝塔Python项目配置
class BaoTaConfig:
    """宝塔面板配置"""
    
    # 基础配置
    SECRET_KEY = "xhs-backend-baota-secret-key-2024"
    JWT_SECRET_KEY = "xhs-jwt-baota-secret-key-2024"
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = f"sqlite:///{os.path.join(BASE_DIR, 'data', 'data.sqlite')}"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Flask配置
    DEBUG = False
    TESTING = False
    
    # JWT配置
    from datetime import timedelta
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(days=1)
    
    # API配置
    MIDJOURNEY_API_KEY = "sk-435z6Pm6jimUgx5LhNHGAH7zZOjXLbnEsWT9CE84BjeonfvF"
    MIDJOURNEY_API_BASE = "https://xuedingmao.online"
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    UPLOAD_FOLDER = os.path.join(BASE_DIR, 'app', 'uploads')
    
    # 确保必要目录存在
    @staticmethod
    def init_app(app):
        """初始化应用"""
        # 创建必要目录
        dirs = [
            os.path.join(BASE_DIR, 'data'),
            os.path.join(BASE_DIR, 'logs'),
            os.path.join(BASE_DIR, 'app', 'uploads'),
            os.path.join(BASE_DIR, 'app', 'uploads', 'software'),
            os.path.join(BASE_DIR, 'app', 'static'),
            os.path.join(BASE_DIR, 'app', 'static', 'admin')
        ]
        
        for directory in dirs:
            os.makedirs(directory, exist_ok=True)
        
        # 配置日志
        if not app.debug:
            import logging
            from logging.handlers import RotatingFileHandler
            
            log_dir = os.path.join(BASE_DIR, 'logs')
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
                
            file_handler = RotatingFileHandler(
                os.path.join(log_dir, 'backend_admin.log'),
                maxBytes=10240000,  # 10MB
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('XHS Backend Admin startup')

# 导出配置
config = BaoTaConfig
