#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宝塔面板专用启动脚本
"""
import os
import sys

# 设置环境变量
os.environ['FLASK_CONFIG'] = 'baota'

# 添加项目路径
project_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_path)

from app import create_app, db
from app.models import User, MembershipPlan

def init_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    
    # 创建所有表
    db.create_all()
    
    # 检查是否已有管理员
    admin = User.query.filter_by(is_admin=True).first()
    if not admin:
        print("创建默认管理员...")
        admin = User(
            username="admin",
            email="<EMAIL>",
            is_admin=True,
            is_paid_member=True
        )
        admin.set_password("admin123")
        db.session.add(admin)
        
        # 创建默认会员计划
        plans = [
            {
                "name": "月度会员",
                "description": "享受所有功能，月度订阅",
                "price": 29.9,
                "duration_months": 1
            },
            {
                "name": "季度会员", 
                "description": "享受所有功能，季度订阅，更优惠",
                "price": 79.9,
                "duration_months": 3
            },
            {
                "name": "年度会员",
                "description": "享受所有功能，年度订阅，最优惠",
                "price": 299.9,
                "duration_months": 12
            }
        ]
        
        for plan_data in plans:
            if not MembershipPlan.query.filter_by(name=plan_data["name"]).first():
                plan = MembershipPlan(**plan_data)
                db.session.add(plan)
        
        db.session.commit()
        print("数据库初始化完成！")
    else:
        print("管理员已存在，跳过初始化")

def create_application():
    """创建应用实例"""
    app = create_app('baota')
    
    with app.app_context():
        init_database()
    
    return app

# 创建应用实例
application = create_application()
app = application  # 宝塔需要的变量名

if __name__ == '__main__':
    print("启动XHS后端管理系统...")
    print("访问地址: http://your-domain.com")
    print("管理后台: http://your-domain.com/admin")
    print("默认账号: admin / admin123")
    app.run(host='0.0.0.0', port=5000, debug=False)
