#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""API测试脚本"""
import requests
import json

BASE_URL = "http://localhost:5000"

def test_main_page():
    """测试主页"""
    print("🔍 测试主页...")
    response = requests.get(f"{BASE_URL}/")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print("✅ 主页访问成功")
    else:
        print("❌ 主页访问失败")
    print()

def test_admin_login():
    """测试管理员登录"""
    print("🔍 测试管理员登录...")
    
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get("success"):
            print("✅ 管理员登录成功")
            print(f"用户信息: {data['user']['username']} (管理员: {data['user']['is_admin']})")
            return data["access_token"]
        else:
            print(f"❌ 登录失败: {data.get('message')}")
    else:
        print("❌ 登录请求失败")
    print()
    return None

def test_admin_stats(token):
    """测试管理员统计"""
    if not token:
        print("⚠️ 跳过统计测试 - 无有效token")
        return
    
    print("🔍 测试管理员统计...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/admin/stats", headers=headers)
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get("success"):
            print("✅ 统计数据获取成功")
            stats = data["stats"]
            print(f"总用户数: {stats['total_users']}")
            print(f"付费用户数: {stats['paid_users']}")
            print(f"活跃会员数: {stats['active_paid_users']}")
            print(f"软件数量: {stats['total_software']}")
            print(f"AI任务数: {stats['total_tasks']}")
        else:
            print(f"❌ 获取统计失败: {data.get('message')}")
    else:
        print("❌ 统计请求失败")
    print()

def test_software_list(token):
    """测试软件列表"""
    if not token:
        print("⚠️ 跳过软件列表测试 - 无有效token")
        return
    
    print("🔍 测试软件列表...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/software/list", headers=headers)
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get("success"):
            print("✅ 软件列表获取成功")
            print(f"软件数量: {len(data['software'])}")
        else:
            print(f"❌ 获取软件列表失败: {data.get('message')}")
    else:
        print("❌ 软件列表请求失败")
    print()

def test_user_registration():
    """测试用户注册"""
    print("🔍 测试用户注册...")
    
    register_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/register", json=register_data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 201:
        data = response.json()
        if data.get("success"):
            print("✅ 用户注册成功")
            print(f"新用户: {data['user']['username']}")
        else:
            print(f"❌ 注册失败: {data.get('message')}")
    else:
        print("❌ 注册请求失败")
    print()

def main():
    """主测试函数"""
    print("🚀 XHS 后端管理系统 API 测试")
    print("=" * 50)
    
    # 测试主页
    test_main_page()
    
    # 测试管理员登录
    token = test_admin_login()
    
    # 测试管理员功能
    test_admin_stats(token)
    test_software_list(token)
    
    # 测试用户注册
    test_user_registration()
    
    print("=" * 50)
    print("🎉 API测试完成!")
    print("=" * 50)
    print("访问地址:")
    print("主页: http://localhost:5000")
    print("管理后台: http://localhost:5000/admin")
    print("默认管理员: admin / admin123")

if __name__ == "__main__":
    main()
