#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宝塔面板专用启动文件
"""
import os
import sys

# 添加项目路径到Python路径
project_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_path)

from app import create_app, db

# 创建应用实例
application = create_app()
app = application  # 宝塔需要的变量名

# 确保数据库表存在
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
