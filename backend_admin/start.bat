@echo off
chcp 65001 >nul
echo 🚀 启动 XHS 后端管理系统...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查是否在正确的目录
if not exist "run.py" (
    echo ❌ 错误: 请在backend_admin目录下运行此脚本
    pause
    exit /b 1
)

REM 安装依赖
echo 📦 检查并安装依赖...
python -m pip install -r requirements.txt

REM 初始化数据库
if not exist "data\data.sqlite" (
    echo 🗄️ 初始化数据库...
    python create_admin.py
)

REM 启动服务
echo 🌟 启动服务...
echo 访问地址: http://localhost:5000
echo 管理后台: http://localhost:5000/admin
echo 默认账号: admin / admin123
echo 按 Ctrl+C 停止服务
echo ==================================================

python run.py

pause
