# XHS 后端管理系统

这是一个基于Flask的后端管理系统，用于管理小红书自动发布和AI图片生成平台。

## 功能特点

- 用户管理和认证
- 会员管理系统
- 软件版本管理和分发
- Midjourney API集成
- 完整的Web管理后台
- RESTful API接口

## 技术栈

- **后端框架**: Flask
- **数据库**: SQLAlchemy + SQLite
- **认证**: JWT (Flask-JWT-Extended)
- **前端**: Bootstrap 5 + JavaScript
- **API**: RESTful设计

## 目录结构

```
backend_admin/
├── app/                    # 应用主目录
│   ├── __init__.py        # 应用初始化
│   ├── models.py          # 数据模型
│   ├── routes/            # 路由模块
│   │   ├── __init__.py
│   │   ├── auth.py        # 认证路由
│   │   ├── admin.py       # 管理员API
│   │   ├── admin_dashboard.py  # 管理后台
│   │   ├── midjourney.py  # AI图片生成
│   │   ├── software.py    # 软件管理
│   │   └── main.py        # 主路由
│   ├── templates/         # HTML模板
│   │   └── admin/         # 管理后台模板
│   ├── static/           # 静态文件
│   │   └── admin/        # 管理后台静态文件
│   ├── uploads/          # 上传文件
│   └── utils/            # 工具模块
├── config.py             # 配置文件
├── requirements.txt      # 依赖包
├── run.py               # 应用启动文件
├── create_admin.py      # 创建管理员脚本
└── deploy.py            # 部署脚本
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 初始化数据库

```bash
python create_admin.py
```

### 3. 启动服务

```bash
python run.py
```

### 4. 访问管理后台

打开浏览器访问: http://localhost:5000/admin

默认管理员账号:
- 用户名: admin
- 密码: admin123

## API文档

### 认证接口

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/refresh` - 刷新令牌
- `GET /api/auth/profile` - 获取用户信息

### 软件管理

- `GET /api/software/list` - 获取软件列表
- `GET /api/software/<id>` - 获取软件详情
- `GET /api/software/download/<version_id>` - 下载软件

### AI图片生成

- `POST /api/midjourney/generate` - 生成图片
- `GET /api/midjourney/tasks/<id>` - 获取任务状态
- `GET /api/midjourney/tasks` - 获取用户任务列表

### 管理员接口

- `GET /admin/stats` - 获取统计数据
- `GET /admin/users` - 用户管理
- `GET /admin/software` - 软件管理
- `GET /admin/membership-plans` - 会员计划管理

## 配置说明

### 环境变量

- `FLASK_CONFIG`: 运行环境 (development/testing/production)
- `SECRET_KEY`: Flask密钥
- `JWT_SECRET_KEY`: JWT密钥
- `DATABASE_URL`: 数据库连接URL
- `MIDJOURNEY_API_KEY`: Midjourney API密钥
- `MIDJOURNEY_API_BASE`: Midjourney API基础URL

### 配置文件

编辑 `config.py` 文件来修改配置:

```python
class Config:
    SECRET_KEY = "your-secret-key"
    MIDJOURNEY_API_KEY = "your-api-key"
    MIDJOURNEY_API_BASE = "https://api.example.com"
```

## 部署

### 开发环境

```bash
python run.py
```

### 生产环境

```bash
# 使用Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 run:app

# 或使用uWSGI
pip install uwsgi
uwsgi --http :5000 --module run:app
```

## 注意事项

- 首次运行需要创建管理员账号
- 确保API密钥配置正确
- 生产环境请修改默认密码
- 定期备份数据库文件

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库文件权限
   - 确认SQLite文件路径正确

2. **API调用失败**
   - 验证API密钥是否正确
   - 检查网络连接
   - 查看API配额是否用完

3. **权限错误**
   - 确认用户是否有管理员权限
   - 检查JWT令牌是否有效

### 日志查看

应用日志会输出到控制台，包含详细的错误信息和调试信息。

## 更新日志

### v1.0.0
- 初始版本发布
- 基础用户管理功能
- Midjourney API集成
- Web管理后台

## 许可证

本项目采用 MIT 许可证。
