#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""应用启动文件"""
import os
from app import create_app, db
from app.models import User, Software, SoftwareVersion, MembershipPlan, MidjourneyTask

# 创建应用实例
app = create_app()

@app.shell_context_processor
def make_shell_context():
    """Shell上下文处理器"""
    return {
        'db': db,
        'User': User,
        'Software': Software,
        'SoftwareVersion': SoftwareVersion,
        'MembershipPlan': MembershipPlan,
        'MidjourneyTask': MidjourneyTask
    }

@app.cli.command()
def init_db():
    """初始化数据库"""
    db.create_all()
    print("数据库初始化完成")

@app.cli.command()
def create_admin():
    """创建管理员账号"""
    username = input("请输入管理员用户名 (默认: admin): ") or "admin"
    email = input("请输入管理员邮箱 (默认: <EMAIL>): ") or "<EMAIL>"
    password = input("请输入管理员密码 (默认: admin123): ") or "admin123"
    
    # 检查用户是否已存在
    if User.query.filter_by(username=username).first():
        print(f"用户 {username} 已存在")
        return
    
    if User.query.filter_by(email=email).first():
        print(f"邮箱 {email} 已存在")
        return
    
    # 创建管理员用户
    admin = User(username=username, email=email, is_admin=True, is_paid_member=True)
    admin.set_password(password)
    
    db.session.add(admin)
    db.session.commit()
    
    print(f"管理员账号创建成功:")
    print(f"用户名: {username}")
    print(f"邮箱: {email}")
    print(f"密码: {password}")

if __name__ == '__main__':
    # 在应用上下文中创建数据库表
    with app.app_context():
        db.create_all()
        print("数据库表创建完成")
    
    # 启动应用
    app.run(host='0.0.0.0', port=5000, debug=True)
